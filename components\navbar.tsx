"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { ArrowRight, TrendingUp, Moon, Sun, DollarSign, Zap, Users, Award, Globe, User, LogIn, Flame, Gift, Newspaper, ChevronDown } from "lucide-react"
import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { useLanguage } from "@/contexts/language-context"
import LanguageSelector from "@/components/language-selector"

export default function Navbar() {
  const [isDarkMode, setIsDarkMode] = useState(true)
  const { t } = useLanguage()
  const pathname = usePathname()

  // Check system preference on initial load
  useEffect(() => {
    if (typeof window !== "undefined") {
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
      setIsDarkMode(prefersDark)
    }
  }, [])

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode)
    document.documentElement.classList.toggle("dark")
  }

  // Hide navbar on dashboard pages
  if (pathname?.startsWith('/dashboard')) {
    return null
  }

  return (
    <nav
      className="fixed top-4 left-4 right-4 z-40 flex items-center justify-between bg-transparent backdrop-blur-sm border border-gray-200/30 dark:border-white/10 rounded-2xl px-3 py-2 shadow-sm"
      role="navigation"
      aria-label="Main navigation"
    >
      {/* Logo */}
      <Link href="/" className="flex items-center gap-2 group">
        <div className="w-8 h-8 md:w-10 md:h-10 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
          <Image
            src="https://res.cloudinary.com/dufcjjaav/image/upload/v1754115839/180_3_3_ygapel.png"
            alt="Forex Throne Logo"
            width={40}
            height={40}
            className="w-full h-full object-contain"
            priority
          />
        </div>
        <div className="hidden sm:block">
          <h1 className="text-lg md:text-xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
            Forex Throne
          </h1>
          <p className="text-xs text-gray-600 dark:text-white/60">Prop Trading Firm</p>
        </div>
      </Link>

      {/* Navigation Links */}
      <div className="flex items-center gap-2 md:gap-4">
        <LanguageSelector />
        <Button
          variant="ghost"
          onClick={toggleTheme}
          className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg group"
          aria-label="Toggle between light and dark theme"
        >
          <div className="group-hover:rotate-180 transition-transform duration-500">
            {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
          </div>
        </Button>
        
        {/* Markets & Calendar */}
        <Link href="/trading-symbols">
          <Button
            variant="ghost"
            className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
          >
            {t("nav.markets")}
          </Button>
        </Link>
        <Link href="/economic-calendar">
          <Button
            variant="ghost"
            className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
          >
            {t("nav.calendar")}
          </Button>
        </Link>

        {/* More Options Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
            >
              More
              <ChevronDown className="h-3 w-3 ml-1" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700">
            <DropdownMenuItem asChild>
              <Link href="/how-it-works" className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                <Zap className="h-4 w-4 mr-2" />
                How It Works
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/challenges" className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                <Award className="h-4 w-4 mr-2" />
                Challenges
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/faqs" className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                <Globe className="h-4 w-4 mr-2" />
                FAQs
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/affiliate" className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                <Users className="h-4 w-4 mr-2" />
                {t("nav.affiliate")}
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/giveaway" className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                <Gift className="h-4 w-4 mr-2" />
                Giveaway
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/news" className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                <Newspaper className="h-4 w-4 mr-2" />
                News
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Auth Buttons */}
        <Link href="/auth">
          <Button
            variant="ghost"
            className="text-xs md:text-sm font-light text-gray-700 dark:text-white/80 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100/30 dark:hover:bg-white/5 transition-all duration-300 px-2 py-1 rounded-lg"
          >
            <LogIn className="h-3 w-3 mr-1" />
            {t("nav.login")}
          </Button>
        </Link>
        <Link href="/auth?mode=signup">
          <Button className="rounded-lg bg-gray-900 dark:bg-white text-white dark:text-black hover:bg-gray-800 dark:hover:bg-white/90 px-2 md:px-4 py-1 text-xs md:text-sm hover:scale-105 transition-all duration-300 hover:shadow-lg">
            <User className="h-3 w-3 mr-1" />
            {t("nav.getFunded")}
          </Button>
        </Link>
      </div>
    </nav>
  )
} 