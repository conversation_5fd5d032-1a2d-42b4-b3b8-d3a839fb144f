"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  TrendingUp,
  DollarSign,
  User,
  ChevronDown,
  HelpCircle,
  MessageSquare,
  ExternalLink,
  Mic,
  Grid,
  Car,
  Clock,
  BarChart3,
  Trophy,
  Zap,
  Target,
  Rocket,
} from "lucide-react"
import { useState } from "react"
import Image from "next/image"

export default function AccountOverview() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [activePhase, setActivePhase] = useState("phase1")

  const carouselImages = [
    "https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=300&fit=crop",
    "https://images.unsplash.com/photo-**********-b33ff0c44a43?w=400&h=300&fit=crop",
    "https://images.unsplash.com/photo-**********-fcd25c85cd64?w=400&h=300&fit=crop",
    "https://images.unsplash.com/photo-**********-b33ff0c44a43?w=400&h=300&fit=crop"
  ]

  const phaseButtons = [
    { id: "phase1", label: "Phase 1", icon: Target, description: "Evaluation Phase" },
    { id: "phase2", label: "Phase 2", icon: Trophy, description: "Verification Phase" },
    { id: "instant", label: "Instant", icon: Zap, description: "Direct Funding" },
    { id: "hft", label: "HFT", icon: Rocket, description: "High Frequency Trading" },
  ]

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white p-6 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">Accounts</h1>
            <p className="text-blue-100">Unlock your trading potential with FxThrone. Start trading now!</p>
          </div>
          <div className="flex items-center gap-4">
            <Button className="bg-white text-blue-600 hover:bg-gray-100">
              Start Challenge
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Phase Buttons */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {phaseButtons.map((phase) => {
              const IconComponent = phase.icon
              return (
                <Button
                  key={phase.id}
                  onClick={() => setActivePhase(phase.id)}
                  className={`h-20 flex flex-col items-center justify-center gap-2 transition-all duration-200 ${
                    activePhase === phase.id
                      ? "bg-blue-600 text-white"
                      : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                  }`}
                >
                  <IconComponent className="w-5 h-5" />
                  <div className="text-center">
                    <div className="font-semibold text-sm">{phase.label}</div>
                    <div className="text-xs opacity-75">{phase.description}</div>
                  </div>
                </Button>
              )
            })}
          </div>

          {/* Sale Banner - Thin Moving Animation */}
          <div className="relative overflow-hidden bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-lg">
            <div className="h-12 flex items-center justify-center relative">
              <div className="animate-marquee whitespace-nowrap flex items-center gap-8">
                <span className="text-sm font-semibold">🎉 SPECIAL OFFER - UP TO 70% OFF!</span>
                <span className="text-sm">Use coupon code: <span className="font-bold text-yellow-300">SAVE70</span></span>
                <span className="text-sm">Limited time offer - Don't miss out!</span>
                <span className="text-sm font-semibold">🎉 SPECIAL OFFER - UP TO 70% OFF!</span>
                <span className="text-sm">Use coupon code: <span className="font-bold text-yellow-300">SAVE70</span></span>
                <span className="text-sm">Limited time offer - Don't miss out!</span>
              </div>
            </div>
          </div>

          {/* Account Info Box */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-bold text-gray-900 dark:text-white">Free Dashboard Tour | Login: 00000</p>
                <p className="text-gray-600 dark:text-gray-300 mt-1">You are given a demo account to experience the dashboard</p>
              </div>
              <Button className="bg-purple-100 text-purple-700 hover:bg-purple-200 dark:bg-purple-900/30 dark:text-purple-300">
                Dashboard
              </Button>
            </div>
          </div>
        </div>

        {/* Right Sidebar */}
        <div className="space-y-6">
          {/* Action Buttons */}
          <div className="space-y-4">
            <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white h-16 rounded-lg flex items-center justify-between px-4">
              <div className="flex items-center gap-3">
                <HelpCircle className="w-5 h-5" />
                <div className="text-left">
                  <p className="font-semibold">Trading Rules & Guidelines</p>
                </div>
              </div>
              <ExternalLink className="w-4 h-4" />
            </Button>
          </div>

          {/* Image Carousel */}
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Gallery</h3>
            <div className="relative">
              <div className="w-full h-80 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
                <Image
                  src={carouselImages[currentImageIndex]}
                  alt="Trading platform"
                  width={400}
                  height={320}
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* Carousel Navigation */}
              <div className="flex justify-center gap-2 mt-4">
                {carouselImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      currentImageIndex === index 
                        ? 'bg-blue-500' 
                        : 'bg-gray-300 dark:bg-gray-600'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Custom CSS for marquee animation */}
      <style jsx>{`
        @keyframes marquee {
          0% {
            transform: translateX(100%);
          }
          100% {
            transform: translateX(-100%);
          }
        }
        .animate-marquee {
          animation: marquee 20s linear infinite;
        }
      `}</style>
    </div>
  )
}
